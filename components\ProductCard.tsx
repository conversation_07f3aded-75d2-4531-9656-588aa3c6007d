"use client"

import type React from "react"

import { useState, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import type { Product } from "@/types/product"
import { useCart } from "@/contexts/CartContext"
import { useWishlist } from "@/contexts/WishlistContext"
import { useComparison } from "@/contexts/ComparisonContext"
import { useRecentlyViewed } from "@/hooks/useRecentlyViewed"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ShoppingCart, Heart, Eye, Star, Scale } from "lucide-react"
import { motion } from "framer-motion"
import { useToast } from "@/hooks/use-toast"
import { QuickViewModal } from "./QuickViewModal"

interface ProductCardProps {
  product: Product
  index?: number
}

export default function ProductCard({ product, index = 0 }: ProductCardProps) {
  const { dispatch: cartDispatch } = useCart()
  const { dispatch: wishlistDispatch, isInWishlist } = useWishlist()
  const { dispatch: comparisonDispatch, isInComparison } = useComparison()
  const { addToRecentlyViewed } = useRecentlyViewed()
  const { toast } = useToast()
  const [isHovered, setIsHovered] = useState(false)
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false)

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    cartDispatch({ type: "ADD_ITEM", payload: product })
    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
  }

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    if (isInWishlist(product.id)) {
      wishlistDispatch({ type: "REMOVE_ITEM", payload: product.id })
      toast({
        title: "Removed from wishlist",
        description: `${product.name} has been removed from your wishlist.`,
      })
    } else {
      wishlistDispatch({ type: "ADD_ITEM", payload: product.id })
      toast({
        title: "Added to wishlist",
        description: `${product.name} has been added to your wishlist.`,
      })
    }
  }

  const handleComparisonToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    if (isInComparison(product.id)) {
      comparisonDispatch({ type: "REMOVE_ITEM", payload: product.id })
      toast({
        title: "Removed from comparison",
        description: `${product.name} has been removed from comparison.`,
      })
    } else {
      comparisonDispatch({ type: "ADD_ITEM", payload: product })
      toast({
        title: "Added to comparison",
        description: `${product.name} has been added to comparison.`,
      })
    }
  }

  const handleProductClick = useCallback(() => {
    addToRecentlyViewed(product)
  }, [product])

  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        whileHover={{ y: -5 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Card className="group overflow-hidden border-0 shadow-md hover:shadow-xl transition-all duration-300">
          <div className="relative overflow-hidden">
            <Link href={`/products/${product.id}`} onClick={handleProductClick}>
              <Image
                src={product.image || "/placeholder.svg"}
                alt={product.name}
                width={400}
                height={400}
                className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </Link>

            {discountPercentage > 0 && (
              <Badge className="absolute top-2 left-2 bg-red-500 hover:bg-red-600">-{discountPercentage}%</Badge>
            )}

            <motion.div
              className="absolute top-2 right-2 flex flex-col gap-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: isHovered ? 1 : 0, x: isHovered ? 0 : 20 }}
              transition={{ duration: 0.2 }}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={isInWishlist(product.id) ? "default" : "secondary"}
                      className="h-8 w-8"
                      onClick={handleWishlistToggle}
                    >
                      <Heart className={`h-4 w-4 ${isInWishlist(product.id) ? "fill-current" : ""}`} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isInWishlist(product.id) ? "Remove from wishlist" : "Add to wishlist"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant={isInComparison(product.id) ? "default" : "secondary"}
                      className="h-8 w-8"
                      onClick={handleComparisonToggle}
                    >
                      <Scale className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isInComparison(product.id) ? "Remove from comparison" : "Add to comparison"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="secondary"
                      className="h-8 w-8"
                      onClick={() => setIsQuickViewOpen(true)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Quick view</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>

            <motion.div
              className="absolute bottom-2 left-2 right-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 20 }}
              transition={{ duration: 0.2 }}
            >
              <Button onClick={handleAddToCart} className="w-full" size="sm">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>
            </motion.div>
          </div>

          <CardContent className="p-4">
            <div className="flex items-center gap-1 mb-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(product.rating) ? "text-yellow-400 fill-current" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">({product.reviews})</span>
            </div>

            <Link href={`/products/${product.id}`} onClick={handleProductClick}>
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 hover:text-primary transition-colors">
                {product.name}
              </h3>
            </Link>

            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{product.description}</p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-xl font-bold">${product.price}</span>
                {product.originalPrice && (
                  <span className="text-sm text-muted-foreground line-through">${product.originalPrice}</span>
                )}
              </div>
              <Badge variant={product.inStock ? "default" : "secondary"}>
                {product.inStock ? "In Stock" : "Out of Stock"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <QuickViewModal product={product} isOpen={isQuickViewOpen} onClose={() => setIsQuickViewOpen(false)} />
    </>
  )
}
