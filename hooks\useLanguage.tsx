"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

type Language = "en" | "es" | "fr" | "de"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  en: {
    "nav.products": "Products",
    "nav.categories": "Categories",
    "nav.deals": "Deals",
    "nav.cart": "Cart",
    "nav.wishlist": "Wishlist",
    "nav.compare": "Compare",
    "product.addToCart": "Add to Cart",
    "product.addToWishlist": "Add to Wishlist",
    "product.compare": "Compare",
    "product.quickView": "Quick View",
    "product.inStock": "In Stock",
    "product.outOfStock": "Out of Stock",
    "cart.empty": "Your cart is empty",
    "cart.total": "Total",
    "cart.checkout": "Checkout",
    "search.placeholder": "Search products...",
    "filter.category": "Category",
    "filter.sortBy": "Sort by",
    "filter.priceRange": "Price Range",
    "reviews.title": "Customer Reviews",
    "reviews.writeReview": "Write a Review",
    "coupon.apply": "Apply Coupon",
    "coupon.invalid": "Invalid coupon code",
    "admin.dashboard": "Admin Dashboard",
    "admin.products": "Products",
    "admin.orders": "Orders",
    "admin.analytics": "Analytics",
  },
  es: {
    "nav.products": "Productos",
    "nav.categories": "Categorías",
    "nav.deals": "Ofertas",
    "nav.cart": "Carrito",
    "nav.wishlist": "Lista de Deseos",
    "nav.compare": "Comparar",
    "product.addToCart": "Añadir al Carrito",
    "product.addToWishlist": "Añadir a Lista de Deseos",
    "product.compare": "Comparar",
    "product.quickView": "Vista Rápida",
    "product.inStock": "En Stock",
    "product.outOfStock": "Agotado",
    "cart.empty": "Tu carrito está vacío",
    "cart.total": "Total",
    "cart.checkout": "Finalizar Compra",
    "search.placeholder": "Buscar productos...",
    "filter.category": "Categoría",
    "filter.sortBy": "Ordenar por",
    "filter.priceRange": "Rango de Precio",
    "reviews.title": "Reseñas de Clientes",
    "reviews.writeReview": "Escribir Reseña",
    "coupon.apply": "Aplicar Cupón",
    "coupon.invalid": "Código de cupón inválido",
    "admin.dashboard": "Panel de Administración",
    "admin.products": "Productos",
    "admin.orders": "Pedidos",
    "admin.analytics": "Analíticas",
  },
  fr: {
    "nav.products": "Produits",
    "nav.categories": "Catégories",
    "nav.deals": "Offres",
    "nav.cart": "Panier",
    "nav.wishlist": "Liste de Souhaits",
    "nav.compare": "Comparer",
    "product.addToCart": "Ajouter au Panier",
    "product.addToWishlist": "Ajouter à la Liste de Souhaits",
    "product.compare": "Comparer",
    "product.quickView": "Aperçu Rapide",
    "product.inStock": "En Stock",
    "product.outOfStock": "Rupture de Stock",
    "cart.empty": "Votre panier est vide",
    "cart.total": "Total",
    "cart.checkout": "Commander",
    "search.placeholder": "Rechercher des produits...",
    "filter.category": "Catégorie",
    "filter.sortBy": "Trier par",
    "filter.priceRange": "Gamme de Prix",
    "reviews.title": "Avis Clients",
    "reviews.writeReview": "Écrire un Avis",
    "coupon.apply": "Appliquer le Coupon",
    "coupon.invalid": "Code de coupon invalide",
    "admin.dashboard": "Tableau de Bord Admin",
    "admin.products": "Produits",
    "admin.orders": "Commandes",
    "admin.analytics": "Analytiques",
  },
  de: {
    "nav.products": "Produkte",
    "nav.categories": "Kategorien",
    "nav.deals": "Angebote",
    "nav.cart": "Warenkorb",
    "nav.wishlist": "Wunschliste",
    "nav.compare": "Vergleichen",
    "product.addToCart": "In den Warenkorb",
    "product.addToWishlist": "Zur Wunschliste",
    "product.compare": "Vergleichen",
    "product.quickView": "Schnellansicht",
    "product.inStock": "Auf Lager",
    "product.outOfStock": "Nicht Verfügbar",
    "cart.empty": "Ihr Warenkorb ist leer",
    "cart.total": "Gesamt",
    "cart.checkout": "Zur Kasse",
    "search.placeholder": "Produkte suchen...",
    "filter.category": "Kategorie",
    "filter.sortBy": "Sortieren nach",
    "filter.priceRange": "Preisbereich",
    "reviews.title": "Kundenbewertungen",
    "reviews.writeReview": "Bewertung Schreiben",
    "coupon.apply": "Gutschein Anwenden",
    "coupon.invalid": "Ungültiger Gutscheincode",
    "admin.dashboard": "Admin Dashboard",
    "admin.products": "Produkte",
    "admin.orders": "Bestellungen",
    "admin.analytics": "Analytik",
  },
}

const LanguageContext = createContext<LanguageContextType | null>(null)

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>("en")

  useEffect(() => {
    const saved = localStorage.getItem("language") as Language
    if (saved && translations[saved]) {
      setLanguage(saved)
    }
  }, [])

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem("language", lang)
  }

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)[typeof language]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
