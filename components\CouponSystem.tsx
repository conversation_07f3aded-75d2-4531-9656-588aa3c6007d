"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tag, Check, X } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useToast } from "@/hooks/use-toast"
import { coupons } from "@/lib/data"
import type { Coupon } from "@/types/product"

interface CouponSystemProps {
  subtotal: number
  onCouponApplied: (discount: number, coupon: Coupon) => void
  onCouponRemoved: () => void
  appliedCoupon?: Coupon | null
}

export default function CouponSystem({ subtotal, onCouponApplied, onCouponRemoved, appliedCoupon }: CouponSystemProps) {
  const { toast } = useToast()
  const [couponCode, setCouponCode] = useState("")
  const [isValidating, setIsValidating] = useState(false)

  const validateCoupon = (code: string): { valid: boolean; coupon?: Coupon; error?: string } => {
    const coupon = coupons.find((c) => c.code.toLowerCase() === code.toLowerCase())

    if (!coupon) {
      return { valid: false, error: "Invalid coupon code" }
    }

    if (!coupon.active) {
      return { valid: false, error: "This coupon is no longer active" }
    }

    if (new Date(coupon.expiryDate) < new Date()) {
      return { valid: false, error: "This coupon has expired" }
    }

    if (coupon.usedCount >= coupon.usageLimit) {
      return { valid: false, error: "This coupon has reached its usage limit" }
    }

    if (subtotal < coupon.minOrder) {
      return { valid: false, error: `Minimum order amount is $${coupon.minOrder}` }
    }

    return { valid: true, coupon }
  }

  const calculateDiscount = (coupon: Coupon, subtotal: number): number => {
    if (coupon.type === "percentage") {
      const discount = (subtotal * coupon.value) / 100
      return coupon.maxDiscount ? Math.min(discount, coupon.maxDiscount) : discount
    } else {
      return Math.min(coupon.value, subtotal)
    }
  }

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) return

    setIsValidating(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const validation = validateCoupon(couponCode)

    if (validation.valid && validation.coupon) {
      const discount = calculateDiscount(validation.coupon, subtotal)
      onCouponApplied(discount, validation.coupon)
      toast({
        title: "Coupon applied!",
        description: `You saved $${discount.toFixed(2)} with code ${validation.coupon.code}`,
      })
      setCouponCode("")
    } else {
      toast({
        title: "Invalid coupon",
        description: validation.error,
        variant: "destructive",
      })
    }

    setIsValidating(false)
  }

  const handleRemoveCoupon = () => {
    onCouponRemoved()
    toast({
      title: "Coupon removed",
      description: "The coupon has been removed from your order.",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tag className="h-5 w-5" />
          Promo Code
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <AnimatePresence mode="wait">
          {appliedCoupon ? (
            <motion.div
              key="applied"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-3"
            >
              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">{appliedCoupon.code}</p>
                    <p className="text-sm text-green-600">
                      {appliedCoupon.type === "percentage"
                        ? `${appliedCoupon.value}% off`
                        : `$${appliedCoupon.value} off`}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={handleRemoveCoupon}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="input"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="space-y-3"
            >
              <div className="flex gap-2">
                <Input
                  placeholder="Enter promo code"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                  onKeyPress={(e) => e.key === "Enter" && handleApplyCoupon()}
                />
                <Button onClick={handleApplyCoupon} disabled={!couponCode.trim() || isValidating}>
                  {isValidating ? "Validating..." : "Apply"}
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <Separator />

        <div>
          <p className="text-sm font-medium mb-3">Available Coupons</p>
          <div className="space-y-2">
            {coupons
              .filter((coupon) => coupon.active && new Date(coupon.expiryDate) > new Date())
              .slice(0, 3)
              .map((coupon) => (
                <div
                  key={coupon.id}
                  className="flex items-center justify-between p-2 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                  onClick={() => {
                    setCouponCode(coupon.code)
                    handleApplyCoupon()
                  }}
                >
                  <div>
                    <p className="font-medium text-sm">{coupon.code}</p>
                    <p className="text-xs text-muted-foreground">
                      {coupon.type === "percentage" ? `${coupon.value}% off` : `$${coupon.value} off`}
                      {coupon.minOrder > 0 && ` on orders over $${coupon.minOrder}`}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {Math.ceil((new Date(coupon.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}{" "}
                    days left
                  </Badge>
                </div>
              ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
