"use client"

import { useEffect, useState, useCallback } from "react"
import type { Product } from "@/types/product"

export const useRecentlyViewed = () => {
  const [recentlyViewed, setRecentlyViewed] = useState<Product[]>([])
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const saved = localStorage.getItem("recentlyViewed")
    if (saved) {
      try {
        setRecentlyViewed(JSON.parse(saved))
      } catch (error) {
        console.error("Error loading recently viewed:", error)
      }
    }
    setIsLoaded(true)
  }, [])

  const addToRecentlyViewed = useCallback(
    (product: Product) => {
      if (!isLoaded) return

      setRecentlyViewed((prev) => {
        const filtered = prev.filter((p) => p.id !== product.id)
        const updated = [product, ...filtered].slice(0, 10) // Keep only 10 items

        // Only update localStorage if the array actually changed
        if (JSON.stringify(updated) !== JSON.stringify(prev)) {
          localStorage.setItem("recentlyViewed", JSON.stringify(updated))
        }

        return updated
      })
    },
    [isLoaded],
  )

  const clearRecentlyViewed = useCallback(() => {
    setRecentlyViewed([])
    localStorage.removeItem("recentlyViewed")
  }, [])

  return {
    recentlyViewed,
    addToRecentlyViewed,
    clearRecentlyViewed,
    isLoaded,
  }
}
