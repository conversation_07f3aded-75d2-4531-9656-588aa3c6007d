"use client"

import Image from "next/image"
import Link from "next/link"
import { useComparison } from "@/contexts/ComparisonContext"
import { useCart } from "@/contexts/CartContext"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { X, ShoppingCart, Star } from "lucide-react"
import { motion } from "framer-motion"
import { useToast } from "@/hooks/use-toast"

export default function ProductComparison() {
  const { state, dispatch } = useComparison()
  const { dispatch: cartDispatch } = useCart()
  const { toast } = useToast()

  const handleRemoveFromComparison = (productId: string) => {
    dispatch({ type: "REMOVE_ITEM", payload: productId })
  }

  const handleAddToCart = (product: any) => {
    cartDispatch({ type: "ADD_ITEM", payload: product })
    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
    })
  }

  if (state.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-md mx-auto"
        >
          <div className="text-6xl mb-6">⚖️</div>
          <h1 className="text-3xl font-bold mb-4">No products to compare</h1>
          <p className="text-muted-foreground mb-8">Add products to compare their features and specifications.</p>
          <Link href="/products">
            <Button size="lg">Browse Products</Button>
          </Link>
        </motion.div>
      </div>
    )
  }

  const allFeatures = Array.from(new Set(state.items.flatMap((product) => product.features)))
  const allSpecs = Array.from(new Set(state.items.flatMap((product) => Object.keys(product.specifications))))

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Product Comparison</h1>
          <Button variant="outline" onClick={() => dispatch({ type: "CLEAR_COMPARISON" })}>
            Clear All
          </Button>
        </div>

        <div className="overflow-x-auto">
          <div className="min-w-max">
            <div
              className="grid grid-cols-1 gap-6"
              style={{ gridTemplateColumns: `300px repeat(${state.items.length}, 250px)` }}
            >
              {/* Header Row */}
              <div className="font-semibold text-lg flex items-center">Products</div>
              {state.items.map((product) => (
                <Card key={product.id} className="relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 z-10"
                    onClick={() => handleRemoveFromComparison(product.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <CardContent className="p-4">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={200}
                      height={200}
                      className="w-full h-32 object-cover rounded-lg mb-4"
                    />
                    <Link href={`/products/${product.id}`}>
                      <h3 className="font-semibold text-sm mb-2 line-clamp-2 hover:text-primary transition-colors">
                        {product.name}
                      </h3>
                    </Link>
                    <div className="text-lg font-bold mb-2">${product.price}</div>
                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.inStock}
                      size="sm"
                      className="w-full"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              ))}

              {/* Rating Row */}
              <div className="font-medium flex items-center">Rating</div>
              {state.items.map((product) => (
                <div key={`rating-${product.id}`} className="flex items-center gap-2 p-4">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(product.rating) ? "text-yellow-400 fill-current" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm">({product.reviews})</span>
                </div>
              ))}

              {/* Price Row */}
              <div className="font-medium flex items-center">Price</div>
              {state.items.map((product) => (
                <div key={`price-${product.id}`} className="p-4">
                  <div className="text-lg font-bold">${product.price}</div>
                  {product.originalPrice && (
                    <div className="text-sm text-muted-foreground line-through">${product.originalPrice}</div>
                  )}
                </div>
              ))}

              {/* Stock Row */}
              <div className="font-medium flex items-center">Availability</div>
              {state.items.map((product) => (
                <div key={`stock-${product.id}`} className="p-4">
                  <Badge variant={product.inStock ? "default" : "secondary"}>
                    {product.inStock ? `In Stock (${product.stock})` : "Out of Stock"}
                  </Badge>
                </div>
              ))}

              {/* Brand Row */}
              <div className="font-medium flex items-center">Brand</div>
              {state.items.map((product) => (
                <div key={`brand-${product.id}`} className="p-4">
                  {product.brand}
                </div>
              ))}

              <Separator className="col-span-full my-4" />

              {/* Features Section */}
              <div className="font-semibold text-lg flex items-center">Features</div>
              <div className="col-span-full" />

              {allFeatures.map((feature) => (
                <>
                  <div key={`feature-label-${feature}`} className="font-medium flex items-center text-sm">
                    {feature}
                  </div>
                  {state.items.map((product) => (
                    <div key={`feature-${product.id}-${feature}`} className="p-4 text-center">
                      {product.features.includes(feature) ? (
                        <Badge variant="default" className="text-xs">
                          ✓
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  ))}
                </>
              ))}

              <Separator className="col-span-full my-4" />

              {/* Specifications Section */}
              <div className="font-semibold text-lg flex items-center">Specifications</div>
              <div className="col-span-full" />

              {allSpecs.map((spec) => (
                <>
                  <div key={`spec-label-${spec}`} className="font-medium flex items-center text-sm">
                    {spec}
                  </div>
                  {state.items.map((product) => (
                    <div key={`spec-${product.id}-${spec}`} className="p-4 text-sm">
                      {product.specifications[spec] || "-"}
                    </div>
                  ))}
                </>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
