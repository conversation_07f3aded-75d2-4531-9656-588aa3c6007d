import { notFound } from "next/navigation"
import { products } from "@/lib/data"
import Header from "@/components/Header"
import ProductDetail from "@/components/ProductDetail"
import Footer from "@/components/Footer"

interface ProductPageProps {
  params: {
    id: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const product = products.find((p) => p.id === params.id)

  if (!product) {
    notFound()
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <ProductDetail product={product} />
      </main>
      <Footer />
    </div>
  )
}

export function generateStaticParams() {
  return products.map((product) => ({
    id: product.id,
  }))
}
