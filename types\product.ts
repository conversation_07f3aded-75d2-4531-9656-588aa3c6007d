export interface ProductVariant {
  id: string
  name: string
  value: string
  price?: number
  stock: number
  sku: string
}

export interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  image: string
  images: string[]
  category: string
  rating: number
  reviews: number
  inStock: boolean
  stock: number
  sku: string
  features: string[]
  specifications: Record<string, string>
  variants: {
    colors?: ProductVariant[]
    sizes?: ProductVariant[]
  }
  tags: string[]
  brand: string
  weight: number
  dimensions: {
    length: number
    width: number
    height: number
  }
}

export interface Review {
  id: string
  productId: string
  userId: string
  userName: string
  rating: number
  title: string
  comment: string
  date: string
  verified: boolean
  helpful: number
}

export interface Category {
  id: string
  name: string
  image: string
  productCount: number
}

export interface Coupon {
  id: string
  code: string
  type: "percentage" | "fixed"
  value: number
  minOrder: number
  maxDiscount?: number
  expiryDate: string
  usageLimit: number
  usedCount: number
  active: boolean
}

export interface WishlistItem {
  id: string
  productId: string
  dateAdded: string
}
