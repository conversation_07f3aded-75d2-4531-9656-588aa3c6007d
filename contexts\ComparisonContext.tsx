"use client"

import type React from "react"
import { createContext, useContext, useReducer, type ReactNode } from "react"
import type { Product } from "@/types/product"

interface ComparisonState {
  items: Product[]
  itemCount: number
}

type ComparisonAction =
  | { type: "ADD_ITEM"; payload: Product }
  | { type: "REMOVE_ITEM"; payload: string }
  | { type: "CLEAR_COMPARISON" }

const ComparisonContext = createContext<{
  state: ComparisonState
  dispatch: React.Dispatch<ComparisonAction>
  isInComparison: (productId: string) => boolean
} | null>(null)

const comparisonReducer = (state: ComparisonState, action: ComparisonAction): ComparisonState => {
  switch (action.type) {
    case "ADD_ITEM": {
      const existingItem = state.items.find((item) => item.id === action.payload.id)
      if (existingItem) return state

      // Limit to 4 items for comparison
      const newItems =
        state.items.length >= 4 ? [...state.items.slice(1), action.payload] : [...state.items, action.payload]

      return {
        items: newItems,
        itemCount: newItems.length,
      }
    }

    case "REMOVE_ITEM": {
      const newItems = state.items.filter((item) => item.id !== action.payload)
      return {
        items: newItems,
        itemCount: newItems.length,
      }
    }

    case "CLEAR_COMPARISON":
      return {
        items: [],
        itemCount: 0,
      }

    default:
      return state
  }
}

export const ComparisonProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(comparisonReducer, {
    items: [],
    itemCount: 0,
  })

  const isInComparison = (productId: string) => {
    return state.items.some((item) => item.id === productId)
  }

  return <ComparisonContext.Provider value={{ state, dispatch, isInComparison }}>{children}</ComparisonContext.Provider>
}

export const useComparison = () => {
  const context = useContext(ComparisonContext)
  if (!context) {
    throw new Error("useComparison must be used within a ComparisonProvider")
  }
  return context
}
