import type { Product, Category, Review, Coupon } from "@/types/product"

export const categories: Category[] = [
  {
    id: "1",
    name: "Electronics",
    image: "/placeholder.svg?height=200&width=300&text=Electronics",
    productCount: 28, // Updated count
  },
  {
    id: "2",
    name: "Fashion",
    image: "/placeholder.svg?height=200&width=300&text=Fashion",
    productCount: 21, // Updated count
  },
  {
    id: "3",
    name: "Home & Garden",
    image: "/placeholder.svg?height=200&width=300&text=Home+Garden",
    productCount: 35, // Updated count
  },
  {
    id: "4",
    name: "Sports",
    image: "/placeholder.svg?height=200&width=300&text=Sports",
    productCount: 18, // Updated count
  },
]

export const products: Product[] = [
  {
    id: "1",
    name: "Premium Wireless Headphones",
    description: "High-quality wireless headphones with noise cancellation and premium sound quality.",
    price: 299.99,
    originalPrice: 399.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Electronics",
    rating: 4.8,
    reviews: 1247,
    inStock: true,
    stock: 45,
    sku: "WH-001",
    features: ["Active Noise Cancellation", "30-hour battery life", "Premium materials", "Wireless charging"],
    specifications: {
      "Battery Life": "30 hours",
      "Charging Time": "2 hours",
      Weight: "250g",
      Connectivity: "Bluetooth 5.0",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 20, sku: "WH-001-BLK" },
        { id: "c2", name: "Color", value: "White", stock: 15, sku: "WH-001-WHT" },
        { id: "c3", name: "Color", value: "Silver", stock: 10, sku: "WH-001-SLV" },
      ],
    },
    tags: ["wireless", "noise-cancelling", "premium", "bluetooth"],
    brand: "AudioTech",
    weight: 0.25,
    dimensions: { length: 20, width: 18, height: 8 },
  },
  {
    id: "2",
    name: "Smart Fitness Watch",
    description: "Advanced fitness tracking with heart rate monitoring, GPS, and smart notifications.",
    price: 249.99,
    originalPrice: 329.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Electronics",
    rating: 4.6,
    reviews: 892,
    inStock: true,
    stock: 32,
    sku: "SW-002",
    features: ["Heart Rate Monitor", "GPS Tracking", "Water Resistant", "7-day battery"],
    specifications: {
      Display: '1.4" AMOLED',
      Battery: "7 days",
      "Water Rating": "5ATM",
      Sensors: "Heart Rate, GPS, Accelerometer",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 15, sku: "SW-002-BLK" },
        { id: "c2", name: "Color", value: "Blue", stock: 10, sku: "SW-002-BLU" },
        { id: "c3", name: "Color", value: "Rose Gold", stock: 7, sku: "SW-002-RG" },
      ],
      sizes: [
        { id: "s1", name: "Size", value: "42mm", stock: 20, sku: "SW-002-42" },
        { id: "s2", name: "Size", value: "46mm", stock: 12, sku: "SW-002-46" },
      ],
    },
    tags: ["fitness", "smartwatch", "gps", "waterproof"],
    brand: "FitTech",
    weight: 0.05,
    dimensions: { length: 4.6, width: 4.6, height: 1.2 },
  },
  {
    id: "3",
    name: "Designer Leather Jacket",
    description: "Premium leather jacket with modern design and exceptional craftsmanship.",
    price: 189.99,
    originalPrice: 259.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Fashion",
    rating: 4.7,
    reviews: 456,
    inStock: true,
    stock: 18,
    sku: "LJ-003",
    features: ["Genuine Leather", "Modern Cut", "Multiple Pockets", "Durable Construction"],
    specifications: {
      Material: "100% Genuine Leather",
      Lining: "Polyester",
      Care: "Professional Clean Only",
      Origin: "Italy",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 8, sku: "LJ-003-BLK" },
        { id: "c2", name: "Color", value: "Brown", stock: 6, sku: "LJ-003-BRN" },
        { id: "c3", name: "Color", value: "Tan", stock: 4, sku: "LJ-003-TAN" },
      ],
      sizes: [
        { id: "s1", name: "Size", value: "S", stock: 3, sku: "LJ-003-S" },
        { id: "s2", name: "Size", value: "M", stock: 6, sku: "LJ-003-M" },
        { id: "s3", name: "Size", value: "L", stock: 5, sku: "LJ-003-L" },
        { id: "s4", name: "Size", value: "XL", stock: 4, sku: "LJ-003-XL" },
      ],
    },
    tags: ["leather", "jacket", "fashion", "premium"],
    brand: "StyleCraft",
    weight: 1.2,
    dimensions: { length: 65, width: 55, height: 3 },
  },
  {
    id: "4",
    name: "Smart Home Security Camera",
    description: "4K security camera with AI detection, night vision, and cloud storage.",
    price: 159.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Electronics",
    rating: 4.5,
    reviews: 723,
    inStock: true,
    stock: 28,
    sku: "SC-004",
    features: ["4K Resolution", "Night Vision", "AI Detection", "Cloud Storage"],
    specifications: {
      Resolution: "4K Ultra HD",
      "Field of View": "130°",
      Storage: "Cloud + Local",
      Connectivity: "Wi-Fi 6",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "White", stock: 20, sku: "SC-004-WHT" },
        { id: "c2", name: "Color", value: "Black", stock: 8, sku: "SC-004-BLK" },
      ],
    },
    tags: ["security", "camera", "4k", "smart-home"],
    brand: "SecureTech",
    weight: 0.3,
    dimensions: { length: 10, width: 6, height: 6 },
  },
  {
    id: "5",
    name: "Ergonomic Office Chair",
    description: "Premium ergonomic office chair with lumbar support and adjustable features.",
    price: 399.99,
    originalPrice: 499.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Home & Garden",
    rating: 4.9,
    reviews: 1156,
    inStock: true,
    stock: 12,
    sku: "OC-005",
    features: ["Lumbar Support", "Adjustable Height", "Breathable Mesh", "10-year Warranty"],
    specifications: {
      "Weight Capacity": "300 lbs",
      "Height Range": '16"-20"',
      Material: "Mesh + Aluminum",
      Warranty: "10 years",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 8, sku: "OC-005-BLK" },
        { id: "c2", name: "Color", value: "Gray", stock: 4, sku: "OC-005-GRY" },
      ],
    },
    tags: ["office", "chair", "ergonomic", "furniture"],
    brand: "ComfortSeating",
    weight: 25,
    dimensions: { length: 70, width: 70, height: 120 },
  },
  {
    id: "6",
    name: "Professional Running Shoes",
    description: "High-performance running shoes with advanced cushioning and support.",
    price: 129.99,
    originalPrice: 179.99,
    image: "/placeholder.svg?height=400&width=400",
    images: [
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    category: "Sports",
    rating: 4.4,
    reviews: 634,
    inStock: true,
    stock: 35,
    sku: "RS-006",
    features: ["Advanced Cushioning", "Breathable Upper", "Durable Outsole", "Lightweight Design"],
    specifications: {
      Weight: "280g",
      Drop: "10mm",
      Upper: "Engineered Mesh",
      Outsole: "Carbon Rubber",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black/White", stock: 15, sku: "RS-006-BW" },
        { id: "c2", name: "Color", value: "Blue/Gray", stock: 12, sku: "RS-006-BG" },
        { id: "c3", name: "Color", value: "Red/Black", stock: 8, sku: "RS-006-RB" },
      ],
      sizes: [
        { id: "s1", name: "Size", value: "7", stock: 5, sku: "RS-006-7" },
        { id: "s2", name: "Size", value: "8", stock: 8, sku: "RS-006-8" },
        { id: "s3", name: "Size", value: "9", stock: 10, sku: "RS-006-9" },
        { id: "s4", name: "Size", value: "10", stock: 7, sku: "RS-006-10" },
        { id: "s5", name: "Size", value: "11", stock: 5, sku: "RS-006-11" },
      ],
    },
    tags: ["running", "shoes", "sports", "performance"],
    brand: "RunPro",
    weight: 0.28,
    dimensions: { length: 32, width: 12, height: 10 },
  },
  {
    id: "7",
    name: "Organic Cotton Bed Sheets Set",
    description: "Luxurious 100% organic cotton bed sheets with deep pockets and silky smooth finish.",
    price: 89.99,
    originalPrice: 129.99,
    image: "/placeholder.svg?height=400&width=400&text=Bed+Sheets",
    images: [
      "/placeholder.svg?height=400&width=400&text=Bed+Sheets+White",
      "/placeholder.svg?height=400&width=400&text=Bed+Sheets+Gray",
      "/placeholder.svg?height=400&width=400&text=Bed+Sheets+Navy",
    ],
    category: "Home & Garden",
    rating: 4.7,
    reviews: 892,
    inStock: true,
    stock: 25,
    sku: "BS-007",
    features: ["100% Organic Cotton", "Deep Pocket Design", "Machine Washable", "Hypoallergenic"],
    specifications: {
      Material: "100% Organic Cotton",
      "Thread Count": "400",
      "Pocket Depth": "16 inches",
      Care: "Machine Wash Cold",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "White", stock: 10, sku: "BS-007-WHT" },
        { id: "c2", name: "Color", value: "Gray", stock: 8, sku: "BS-007-GRY" },
        { id: "c3", name: "Color", value: "Navy", stock: 7, sku: "BS-007-NVY" },
      ],
      sizes: [
        { id: "s1", name: "Size", value: "Queen", stock: 15, sku: "BS-007-Q" },
        { id: "s2", name: "Size", value: "King", stock: 10, sku: "BS-007-K" },
      ],
    },
    tags: ["bedding", "organic", "cotton", "home"],
    brand: "ComfortHome",
    weight: 2.5,
    dimensions: { length: 35, width: 25, height: 8 },
  },
  {
    id: "8",
    name: "Gourmet Coffee Maker with Grinder",
    description: "Professional-grade coffee maker with built-in burr grinder and programmable brewing.",
    price: 349.99,
    originalPrice: 449.99,
    image: "/placeholder.svg?height=400&width=400&text=Coffee+Maker",
    images: [
      "/placeholder.svg?height=400&width=400&text=Coffee+Maker+Front",
      "/placeholder.svg?height=400&width=400&text=Coffee+Maker+Side",
      "/placeholder.svg?height=400&width=400&text=Coffee+Maker+Display",
    ],
    category: "Electronics",
    rating: 4.6,
    reviews: 567,
    inStock: true,
    stock: 18,
    sku: "CM-008",
    features: ["Built-in Burr Grinder", "Programmable Timer", "12-Cup Capacity", "Auto Shut-off"],
    specifications: {
      Capacity: "12 cups",
      "Grinder Type": "Burr Grinder",
      Power: "1200W",
      "Water Tank": "Removable",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Stainless Steel", stock: 12, sku: "CM-008-SS" },
        { id: "c2", name: "Color", value: "Black", stock: 6, sku: "CM-008-BLK" },
      ],
    },
    tags: ["coffee", "kitchen", "appliance", "grinder"],
    brand: "BrewMaster",
    weight: 8.5,
    dimensions: { length: 35, width: 25, height: 40 },
  },
  {
    id: "9",
    name: "Vintage Denim Jacket",
    description: "Classic vintage-style denim jacket with distressed finish and comfortable fit.",
    price: 79.99,
    originalPrice: 119.99,
    image: "/placeholder.svg?height=400&width=400&text=Denim+Jacket",
    images: [
      "/placeholder.svg?height=400&width=400&text=Denim+Jacket+Front",
      "/placeholder.svg?height=400&width=400&text=Denim+Jacket+Back",
      "/placeholder.svg?height=400&width=400&text=Denim+Jacket+Detail",
    ],
    category: "Fashion",
    rating: 4.4,
    reviews: 324,
    inStock: true,
    stock: 42,
    sku: "DJ-009",
    features: ["Vintage Wash", "Classic Fit", "Button Closure", "Chest Pockets"],
    specifications: {
      Material: "100% Cotton Denim",
      Wash: "Stone Washed",
      Fit: "Regular",
      Origin: "USA",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Light Blue", stock: 20, sku: "DJ-009-LB" },
        { id: "c2", name: "Color", value: "Dark Blue", stock: 15, sku: "DJ-009-DB" },
        { id: "c3", name: "Color", value: "Black", stock: 7, sku: "DJ-009-BLK" },
      ],
      sizes: [
        { id: "s1", name: "Size", value: "S", stock: 8, sku: "DJ-009-S" },
        { id: "s2", name: "Size", value: "M", stock: 15, sku: "DJ-009-M" },
        { id: "s3", name: "Size", value: "L", stock: 12, sku: "DJ-009-L" },
        { id: "s4", name: "Size", value: "XL", stock: 7, sku: "DJ-009-XL" },
      ],
    },
    tags: ["denim", "jacket", "vintage", "casual"],
    brand: "UrbanStyle",
    weight: 0.8,
    dimensions: { length: 70, width: 60, height: 2 },
  },
  {
    id: "10",
    name: "Yoga Mat with Alignment Lines",
    description: "Premium non-slip yoga mat with alignment guides for perfect pose positioning.",
    price: 49.99,
    originalPrice: 69.99,
    image: "/placeholder.svg?height=400&width=400&text=Yoga+Mat",
    images: [
      "/placeholder.svg?height=400&width=400&text=Yoga+Mat+Purple",
      "/placeholder.svg?height=400&width=400&text=Yoga+Mat+Green",
      "/placeholder.svg?height=400&width=400&text=Yoga+Mat+Detail",
    ],
    category: "Sports",
    rating: 4.8,
    reviews: 1156,
    inStock: true,
    stock: 67,
    sku: "YM-010",
    features: ["Non-Slip Surface", "Alignment Lines", "Eco-Friendly", "6mm Thickness"],
    specifications: {
      Thickness: "6mm",
      Material: "TPE (Eco-Friendly)",
      Dimensions: '72" x 24"',
      Weight: "2.5 lbs",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Purple", stock: 25, sku: "YM-010-PUR" },
        { id: "c2", name: "Color", value: "Green", stock: 22, sku: "YM-010-GRN" },
        { id: "c3", name: "Color", value: "Blue", stock: 20, sku: "YM-010-BLU" },
      ],
    },
    tags: ["yoga", "fitness", "mat", "exercise"],
    brand: "ZenFit",
    weight: 1.1,
    dimensions: { length: 183, width: 61, height: 0.6 },
  },
  {
    id: "11",
    name: "Wireless Bluetooth Speaker",
    description: "Portable waterproof Bluetooth speaker with 360-degree sound and 20-hour battery.",
    price: 129.99,
    originalPrice: 179.99,
    image: "/placeholder.svg?height=400&width=400&text=Bluetooth+Speaker",
    images: [
      "/placeholder.svg?height=400&width=400&text=Speaker+Black",
      "/placeholder.svg?height=400&width=400&text=Speaker+Blue",
      "/placeholder.svg?height=400&width=400&text=Speaker+Playing",
    ],
    category: "Electronics",
    rating: 4.5,
    reviews: 789,
    inStock: true,
    stock: 34,
    sku: "BS-011",
    features: ["360° Sound", "Waterproof IPX7", "20-Hour Battery", "Voice Assistant"],
    specifications: {
      "Battery Life": "20 hours",
      "Water Rating": "IPX7",
      Connectivity: "Bluetooth 5.0",
      Range: "100 feet",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 15, sku: "BS-011-BLK" },
        { id: "c2", name: "Color", value: "Blue", stock: 12, sku: "BS-011-BLU" },
        { id: "c3", name: "Color", value: "Red", stock: 7, sku: "BS-011-RED" },
      ],
    },
    tags: ["bluetooth", "speaker", "portable", "waterproof"],
    brand: "SoundWave",
    weight: 0.6,
    dimensions: { length: 18, width: 18, height: 7 },
  },
  {
    id: "12",
    name: "Ceramic Dinnerware Set",
    description: "16-piece ceramic dinnerware set with modern design, dishwasher and microwave safe.",
    price: 119.99,
    originalPrice: 159.99,
    image: "/placeholder.svg?height=400&width=400&text=Dinnerware+Set",
    images: [
      "/placeholder.svg?height=400&width=400&text=Dinnerware+White",
      "/placeholder.svg?height=400&width=400&text=Dinnerware+Gray",
      "/placeholder.svg?height=400&width=400&text=Dinnerware+Table",
    ],
    category: "Home & Garden",
    rating: 4.6,
    reviews: 445,
    inStock: true,
    stock: 28,
    sku: "DW-012",
    features: ["16-Piece Set", "Dishwasher Safe", "Microwave Safe", "Chip Resistant"],
    specifications: {
      Material: "Stoneware Ceramic",
      "Set Includes": "4 Dinner Plates, 4 Salad Plates, 4 Bowls, 4 Mugs",
      "Dishwasher Safe": "Yes",
      "Microwave Safe": "Yes",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "White", stock: 15, sku: "DW-012-WHT" },
        { id: "c2", name: "Color", value: "Gray", stock: 8, sku: "DW-012-GRY" },
        { id: "c3", name: "Color", value: "Navy", stock: 5, sku: "DW-012-NVY" },
      ],
    },
    tags: ["dinnerware", "ceramic", "kitchen", "dining"],
    brand: "TableCraft",
    weight: 12,
    dimensions: { length: 40, width: 30, height: 15 },
  },
  {
    id: "13",
    name: "Designer Sunglasses",
    description: "Premium polarized sunglasses with UV400 protection and lightweight titanium frame.",
    price: 199.99,
    originalPrice: 299.99,
    image: "/placeholder.svg?height=400&width=400&text=Sunglasses",
    images: [
      "/placeholder.svg?height=400&width=400&text=Sunglasses+Black",
      "/placeholder.svg?height=400&width=400&text=Sunglasses+Tortoise",
      "/placeholder.svg?height=400&width=400&text=Sunglasses+Case",
    ],
    category: "Fashion",
    rating: 4.7,
    reviews: 267,
    inStock: true,
    stock: 45,
    sku: "SG-013",
    features: ["Polarized Lenses", "UV400 Protection", "Titanium Frame", "Anti-Glare"],
    specifications: {
      "Frame Material": "Titanium",
      "Lens Type": "Polarized",
      "UV Protection": "UV400",
      Weight: "25g",
    },
    variants: {
      colors: [
        { id: "c1", name: "Frame Color", value: "Black", stock: 20, sku: "SG-013-BLK" },
        { id: "c2", name: "Frame Color", value: "Tortoise", stock: 15, sku: "SG-013-TOR" },
        { id: "c3", name: "Frame Color", value: "Silver", stock: 10, sku: "SG-013-SLV" },
      ],
    },
    tags: ["sunglasses", "fashion", "uv-protection", "polarized"],
    brand: "LuxVision",
    weight: 0.025,
    dimensions: { length: 15, width: 14, height: 5 },
  },
  {
    id: "14",
    name: "Gaming Mechanical Keyboard",
    description: "RGB backlit mechanical gaming keyboard with customizable keys and macro support.",
    price: 159.99,
    originalPrice: 219.99,
    image: "/placeholder.svg?height=400&width=400&text=Gaming+Keyboard",
    images: [
      "/placeholder.svg?height=400&width=400&text=Keyboard+RGB",
      "/placeholder.svg?height=400&width=400&text=Keyboard+Side",
      "/placeholder.svg?height=400&width=400&text=Keyboard+Keys",
    ],
    category: "Electronics",
    rating: 4.8,
    reviews: 1034,
    inStock: true,
    stock: 52,
    sku: "GK-014",
    features: ["Mechanical Switches", "RGB Backlighting", "Macro Keys", "USB-C Connection"],
    specifications: {
      "Switch Type": "Mechanical Blue",
      Backlighting: "RGB",
      Connection: "USB-C",
      "Key Layout": "Full Size",
    },
    variants: {
      colors: [
        { id: "c1", name: "Color", value: "Black", stock: 30, sku: "GK-014-BLK" },
        { id: "c2", name: "Color", value: "White", stock: 22, sku: "GK-014-WHT" },
      ],
    },
    tags: ["gaming", "keyboard", "mechanical", "rgb"],
    brand: "GameTech",
    weight: 1.2,
    dimensions: { length: 44, width: 13, height: 3 },
  },
  {
    id: "15",
    name: "Resistance Bands Set",
    description: "Complete resistance bands workout set with door anchor and exercise guide.",
    price: 39.99,
    originalPrice: 59.99,
    image: "/placeholder.svg?height=400&width=400&text=Resistance+Bands",
    images: [
      "/placeholder.svg?height=400&width=400&text=Bands+Set",
      "/placeholder.svg?height=400&width=400&text=Bands+Colors",
      "/placeholder.svg?height=400&width=400&text=Bands+Exercise",
    ],
    category: "Sports",
    rating: 4.5,
    reviews: 678,
    inStock: true,
    stock: 89,
    sku: "RB-015",
    features: ["5 Resistance Levels", "Door Anchor", "Exercise Guide", "Carrying Bag"],
    specifications: {
      "Resistance Levels": "5 (10-50 lbs)",
      Material: "Natural Latex",
      "Set Includes": "5 Bands, Door Anchor, Handles, Ankle Straps",
      "Exercise Guide": "Included",
    },
    variants: {
      colors: [{ id: "c1", name: "Set Color", value: "Multi-Color", stock: 89, sku: "RB-015-MC" }],
    },
    tags: ["resistance", "bands", "fitness", "workout"],
    brand: "FitPro",
    weight: 1.5,
    dimensions: { length: 30, width: 20, height: 8 },
  },
  {
    id: "16",
    name: "Succulent Plant Collection",
    description: "Set of 6 assorted succulent plants in decorative pots, perfect for home or office.",
    price: 34.99,
    originalPrice: 49.99,
    image: "/placeholder.svg?height=400&width=400&text=Succulent+Plants",
    images: [
      "/placeholder.svg?height=400&width=400&text=Succulents+Set",
      "/placeholder.svg?height=400&width=400&text=Succulents+Pots",
      "/placeholder.svg?height=400&width=400&text=Succulents+Care",
    ],
    category: "Home & Garden",
    rating: 4.9,
    reviews: 523,
    inStock: true,
    stock: 76,
    sku: "SP-016",
    features: ["6 Different Varieties", "Decorative Pots", "Low Maintenance", "Air Purifying"],
    specifications: {
      "Plant Count": "6 plants",
      "Pot Size": "2.5 inches",
      "Pot Material": "Ceramic",
      "Care Level": "Easy",
    },
    variants: {
      colors: [
        { id: "c1", name: "Pot Color", value: "White", stock: 30, sku: "SP-016-WHT" },
        { id: "c2", name: "Pot Color", value: "Terracotta", stock: 25, sku: "SP-016-TC" },
        { id: "c3", name: "Pot Color", value: "Gray", stock: 21, sku: "SP-016-GRY" },
      ],
    },
    tags: ["plants", "succulents", "home-decor", "garden"],
    brand: "GreenThumb",
    weight: 3.2,
    dimensions: { length: 40, width: 15, height: 12 },
  },
]

export const reviews: Review[] = [
  {
    id: "r1",
    productId: "1",
    userId: "u1",
    userName: "John D.",
    rating: 5,
    title: "Excellent sound quality!",
    comment:
      "These headphones exceeded my expectations. The noise cancellation is fantastic and the battery life is as advertised.",
    date: "2024-01-15",
    verified: true,
    helpful: 24,
  },
  {
    id: "r2",
    productId: "1",
    userId: "u2",
    userName: "Sarah M.",
    rating: 4,
    title: "Great headphones, minor issues",
    comment: "Love the sound quality and comfort. Only issue is the touch controls can be a bit sensitive.",
    date: "2024-01-10",
    verified: true,
    helpful: 12,
  },
  {
    id: "r3",
    productId: "2",
    userId: "u3",
    userName: "Mike R.",
    rating: 5,
    title: "Perfect fitness companion",
    comment: "Tracks everything I need and the battery really does last a week. Highly recommended!",
    date: "2024-01-12",
    verified: true,
    helpful: 18,
  },
  {
    id: "r4",
    productId: "3",
    userId: "u4",
    userName: "Emma L.",
    rating: 5,
    title: "Beautiful leather jacket",
    comment: "The quality is outstanding and it fits perfectly. Worth every penny!",
    date: "2024-01-08",
    verified: true,
    helpful: 15,
  },
  {
    id: "r5",
    productId: "7",
    userId: "u5",
    userName: "Lisa K.",
    rating: 5,
    title: "Incredibly soft and comfortable",
    comment: "These sheets are amazing! So soft and they get even softer after washing. Great quality for the price.",
    date: "2024-01-14",
    verified: true,
    helpful: 22,
  },
  {
    id: "r6",
    productId: "8",
    userId: "u6",
    userName: "Coffee Lover",
    rating: 5,
    title: "Perfect coffee every morning",
    comment:
      "The built-in grinder is fantastic and the programmable timer means I wake up to fresh coffee. Highly recommend!",
    date: "2024-01-13",
    verified: true,
    helpful: 31,
  },
  {
    id: "r7",
    productId: "9",
    userId: "u7",
    userName: "Alex M.",
    rating: 4,
    title: "Great vintage look",
    comment:
      "Love the vintage wash and fit. Quality denim that looks authentic. Runs slightly large so consider sizing down.",
    date: "2024-01-12",
    verified: true,
    helpful: 16,
  },
  {
    id: "r8",
    productId: "10",
    userId: "u8",
    userName: "Yoga Teacher",
    rating: 5,
    title: "Best yoga mat I've owned",
    comment:
      "The alignment lines are incredibly helpful for my students. Great grip and cushioning. Worth every penny!",
    date: "2024-01-11",
    verified: true,
    helpful: 45,
  },
  {
    id: "r9",
    productId: "11",
    userId: "u9",
    userName: "Music Fan",
    rating: 4,
    title: "Great sound quality",
    comment: "Impressive 360-degree sound and the battery really does last 20 hours. Perfect for outdoor activities.",
    date: "2024-01-10",
    verified: true,
    helpful: 28,
  },
  {
    id: "r10",
    productId: "12",
    userId: "u10",
    userName: "Home Chef",
    rating: 5,
    title: "Beautiful and functional",
    comment: "These dishes are gorgeous and very durable. Perfect weight and the colors are exactly as shown.",
    date: "2024-01-09",
    verified: true,
    helpful: 19,
  },
  {
    id: "r11",
    productId: "13",
    userId: "u11",
    userName: "Fashion Forward",
    rating: 5,
    title: "Stylish and protective",
    comment:
      "These sunglasses are incredibly lightweight yet feel premium. The polarization works great and they look amazing.",
    date: "2024-01-08",
    verified: true,
    helpful: 33,
  },
  {
    id: "r12",
    productId: "14",
    userId: "u12",
    userName: "Pro Gamer",
    rating: 5,
    title: "Gaming perfection",
    comment:
      "The mechanical switches feel amazing and the RGB lighting is customizable. Best keyboard I've used for gaming.",
    date: "2024-01-07",
    verified: true,
    helpful: 52,
  },
  {
    id: "r13",
    productId: "15",
    userId: "u13",
    userName: "Fitness Enthusiast",
    rating: 4,
    title: "Great for home workouts",
    comment: "Excellent variety of resistance levels. The door anchor works well and the exercise guide is helpful.",
    date: "2024-01-06",
    verified: true,
    helpful: 24,
  },
  {
    id: "r14",
    productId: "16",
    userId: "u14",
    userName: "Plant Parent",
    rating: 5,
    title: "Perfect starter collection",
    comment:
      "All plants arrived healthy and the pots are beautiful. Great for beginners - they're thriving in my office!",
    date: "2024-01-05",
    verified: true,
    helpful: 37,
  },
]

export const coupons: Coupon[] = [
  {
    id: "c1",
    code: "WELCOME10",
    type: "percentage",
    value: 10,
    minOrder: 50,
    expiryDate: "2024-12-31",
    usageLimit: 1000,
    usedCount: 245,
    active: true,
  },
  {
    id: "c2",
    code: "SAVE25",
    type: "fixed",
    value: 25,
    minOrder: 100,
    maxDiscount: 25,
    expiryDate: "2024-06-30",
    usageLimit: 500,
    usedCount: 123,
    active: true,
  },
  {
    id: "c3",
    code: "SPRING20",
    type: "percentage",
    value: 20,
    minOrder: 75,
    maxDiscount: 50,
    expiryDate: "2024-05-31",
    usageLimit: 200,
    usedCount: 89,
    active: true,
  },
]
