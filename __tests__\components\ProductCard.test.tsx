import { render, screen } from '@testing-library/react'

// Simple component test to verify Jest setup
function TestComponent({ name }: { name: string }) {
  return <div>Hello, {name}!</div>
}

describe('Jest Setup Test', () => {
  it('should render a simple component', () => {
    render(<TestComponent name="World" />)

    expect(screen.getByText('Hello, World!')).toBeInTheDocument()
  })

  it('should handle basic assertions', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toMatch(/ell/)
    expect(['apple', 'banana']).toContain('apple')
  })

  it('should work with async operations', async () => {
    const promise = Promise.resolve('success')
    await expect(promise).resolves.toBe('success')
  })
})
