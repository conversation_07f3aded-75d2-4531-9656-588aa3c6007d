"use client"

import { useState, useMemo } from "react"
import { products } from "@/lib/data"
import ProductCard from "./ProductCard"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Clock, Flame, Star, TrendingDown, Zap } from "lucide-react"
import { motion } from "framer-motion"

export default function DealsPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")

  // Filter products that have original prices (deals)
  const dealProducts = useMemo(() => {
    return products.filter((product) => product.originalPrice && product.originalPrice > product.price)
  }, [])

  // Categorize deals
  const flashDeals = dealProducts.filter((product) => {
    const discount = ((product.originalPrice! - product.price) / product.originalPrice!) * 100
    return discount >= 30
  })

  const bestSellers = dealProducts.filter((product) => product.reviews > 500).slice(0, 8)

  const limitedTime = dealProducts.slice(0, 6)

  const categoryDeals = useMemo(() => {
    if (selectedCategory === "all") return dealProducts
    return dealProducts.filter((product) => product.category.toLowerCase() === selectedCategory)
  }, [dealProducts, selectedCategory])

  const categories = Array.from(new Set(dealProducts.map((p) => p.category)))

  // Mock countdown timer (in a real app, this would be dynamic)
  const getTimeRemaining = () => {
    const now = new Date().getTime()
    const endTime = now + 24 * 60 * 60 * 1000 // 24 hours from now
    const timeLeft = endTime - now

    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000)

    return { hours, minutes, seconds }
  }

  const [timeRemaining] = useState(getTimeRemaining())

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
        {/* Hero Section */}
        <div className="text-center mb-12">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-4xl lg:text-6xl font-bold mb-4"
          >
            Amazing{" "}
            <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">Deals</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-muted-foreground max-w-2xl mx-auto"
          >
            Discover incredible savings on premium products. Limited time offers you don't want to miss!
          </motion.p>
        </div>

        {/* Flash Sale Banner */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-12"
        >
          <Card className="bg-gradient-to-r from-red-500 to-orange-500 text-white border-0">
            <CardContent className="p-8">
              <div className="flex flex-col lg:flex-row items-center justify-between">
                <div className="text-center lg:text-left mb-6 lg:mb-0">
                  <div className="flex items-center justify-center lg:justify-start gap-2 mb-2">
                    <Flame className="h-6 w-6" />
                    <h2 className="text-2xl font-bold">Flash Sale</h2>
                  </div>
                  <p className="text-lg opacity-90">Up to 50% off on selected items</p>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold">{timeRemaining.hours.toString().padStart(2, "0")}</div>
                    <div className="text-sm opacity-75">Hours</div>
                  </div>
                  <div className="text-2xl">:</div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{timeRemaining.minutes.toString().padStart(2, "0")}</div>
                    <div className="text-sm opacity-75">Minutes</div>
                  </div>
                  <div className="text-2xl">:</div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{timeRemaining.seconds.toString().padStart(2, "0")}</div>
                    <div className="text-sm opacity-75">Seconds</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Deal Categories */}
        <Tabs defaultValue="flash" className="mb-12">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="flash" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Flash Deals
            </TabsTrigger>
            <TabsTrigger value="bestsellers" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Best Sellers
            </TabsTrigger>
            <TabsTrigger value="limited" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Limited Time
            </TabsTrigger>
            <TabsTrigger value="category" className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              By Category
            </TabsTrigger>
          </TabsList>

          <TabsContent value="flash" className="mt-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold">Flash Deals - Up to 50% Off</h3>
              <Badge variant="destructive" className="animate-pulse">
                Limited Time
              </Badge>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {flashDeals.map((product, index) => (
                <ProductCard key={product.id} product={product} index={index} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bestsellers" className="mt-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold">Best Selling Deals</h3>
              <Badge variant="secondary">Top Rated</Badge>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {bestSellers.map((product, index) => (
                <ProductCard key={product.id} product={product} index={index} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="limited" className="mt-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold">Limited Time Offers</h3>
              <Badge variant="outline" className="border-orange-500 text-orange-500">
                Ending Soon
              </Badge>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {limitedTime.map((product, index) => (
                <ProductCard key={product.id} product={product} index={index} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="category" className="mt-8">
            <div className="mb-6">
              <h3 className="text-2xl font-bold mb-4">Deals by Category</h3>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  onClick={() => setSelectedCategory("all")}
                  size="sm"
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category.toLowerCase() ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.toLowerCase())}
                    size="sm"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categoryDeals.map((product, index) => (
                <ProductCard key={product.id} product={product} index={index} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Deal Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
        >
          <Card className="text-center">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <TrendingDown className="h-5 w-5 text-green-500" />
                Average Savings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">35%</div>
              <p className="text-muted-foreground">Off regular prices</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Deal Rating
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-yellow-500">4.8</div>
              <p className="text-muted-foreground">Customer satisfaction</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Flame className="h-5 w-5 text-red-500" />
                Active Deals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">{dealProducts.length}</div>
              <p className="text-muted-foreground">Products on sale</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Newsletter Signup for Deal Alerts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-bold mb-4">Never Miss a Deal!</h3>
              <p className="text-lg opacity-90 mb-6">
                Subscribe to get notified about flash sales and exclusive offers
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" className="flex-1 px-4 py-2 rounded-lg text-black" />
                <Button variant="secondary" className="whitespace-nowrap">
                  Subscribe Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  )
}
