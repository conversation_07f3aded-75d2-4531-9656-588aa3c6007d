"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import type { Product } from "@/types/product"
import { useCart } from "@/contexts/CartContext"
import { useWishlist } from "@/contexts/WishlistContext"
import { useComparison } from "@/contexts/ComparisonContext"
import { useRecentlyViewed } from "@/hooks/useRecentlyViewed"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ShoppingCart, Heart, Share2, Star, ChevronLeft, Truck, Shield, RotateCcw, Scale } from "lucide-react"
import { motion } from "framer-motion"
import { useToast } from "@/hooks/use-toast"
import ProductReviews from "./ProductReviews"
import { products } from "@/lib/data"

interface ProductDetailProps {
  product: Product
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const { dispatch: cartDispatch } = useCart()
  const { dispatch: wishlistDispatch, isInWishlist } = useWishlist()
  const { dispatch: comparisonDispatch, isInComparison } = useComparison()
  const { addToRecentlyViewed, recentlyViewed, isLoaded } = useRecentlyViewed()
  const { toast } = useToast()
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [selectedVariants, setSelectedVariants] = useState<Record<string, string>>({})

  useEffect(() => {
    // Only add to recently viewed when the component mounts and isLoaded is true
    if (isLoaded) {
      addToRecentlyViewed(product)
    }
  }, [product.id, isLoaded, addToRecentlyViewed, product])

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      cartDispatch({ type: "ADD_ITEM", payload: product })
    }
    toast({
      title: "Added to cart",
      description: `${quantity} ${product.name}${quantity > 1 ? "s" : ""} added to your cart.`,
    })
  }

  const handleWishlistToggle = () => {
    if (isInWishlist(product.id)) {
      wishlistDispatch({ type: "REMOVE_ITEM", payload: product.id })
      toast({
        title: "Removed from wishlist",
        description: `${product.name} has been removed from your wishlist.`,
      })
    } else {
      wishlistDispatch({ type: "ADD_ITEM", payload: product.id })
      toast({
        title: "Added to wishlist",
        description: `${product.name} has been added to your wishlist.`,
      })
    }
  }

  const handleComparisonToggle = () => {
    if (isInComparison(product.id)) {
      comparisonDispatch({ type: "REMOVE_ITEM", payload: product.id })
      toast({
        title: "Removed from comparison",
        description: `${product.name} has been removed from comparison.`,
      })
    } else {
      comparisonDispatch({ type: "ADD_ITEM", payload: product })
      toast({
        title: "Added to comparison",
        description: `${product.name} has been added to comparison.`,
      })
    }
  }

  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  // Get recommended products (similar category, excluding current product)
  const recommendedProducts = products.filter((p) => p.category === product.category && p.id !== product.id).slice(0, 4)

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
        <Link
          href="/products"
          className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-6"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Products
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="space-y-4">
            <motion.div
              key={selectedImage}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="aspect-square overflow-hidden rounded-lg border"
            >
              <Image
                src={product.images[selectedImage] || "/placeholder.svg"}
                alt={product.name}
                width={600}
                height={600}
                className="w-full h-full object-cover"
              />
            </motion.div>

            <div className="grid grid-cols-4 gap-4">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square overflow-hidden rounded-lg border-2 transition-colors ${
                    selectedImage === index ? "border-primary" : "border-transparent"
                  }`}
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${product.name} ${index + 1}`}
                    width={150}
                    height={150}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-4">{product.name}</h1>

              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating) ? "text-yellow-400 fill-current" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  {product.rating} ({product.reviews} reviews)
                </span>
              </div>

              <div className="flex items-center gap-4 mb-6">
                <span className="text-4xl font-bold">${product.price}</span>
                {product.originalPrice && (
                  <>
                    <span className="text-2xl text-muted-foreground line-through">${product.originalPrice}</span>
                    <Badge className="bg-red-500 hover:bg-red-600">-{discountPercentage}% OFF</Badge>
                  </>
                )}
              </div>

              <Badge variant={product.inStock ? "default" : "secondary"} className="mb-6">
                {product.inStock ? `In Stock (${product.stock} available)` : "Out of Stock"}
              </Badge>

              <p className="text-muted-foreground mb-6">{product.description}</p>
            </div>

            <Separator />

            {/* Product Variants */}
            {(product.variants.colors || product.variants.sizes) && (
              <div className="space-y-4">
                {product.variants.colors && (
                  <div>
                    <label className="font-medium mb-2 block">Color</label>
                    <Select
                      value={selectedVariants.color || ""}
                      onValueChange={(value) => setSelectedVariants({ ...selectedVariants, color: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select color" />
                      </SelectTrigger>
                      <SelectContent>
                        {product.variants.colors.map((variant) => (
                          <SelectItem key={variant.id} value={variant.value}>
                            {variant.value} {variant.stock > 0 ? `(${variant.stock} available)` : "(Out of stock)"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {product.variants.sizes && (
                  <div>
                    <label className="font-medium mb-2 block">Size</label>
                    <Select
                      value={selectedVariants.size || ""}
                      onValueChange={(value) => setSelectedVariants({ ...selectedVariants, size: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select size" />
                      </SelectTrigger>
                      <SelectContent>
                        {product.variants.sizes.map((variant) => (
                          <SelectItem key={variant.id} value={variant.value}>
                            {variant.value} {variant.stock > 0 ? `(${variant.stock} available)` : "(Out of stock)"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <label htmlFor="quantity" className="font-medium">
                  Quantity:
                </label>
                <div className="flex items-center border rounded-md">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="px-4 py-2 hover:bg-muted transition-colors"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-x min-w-[60px] text-center">{quantity}</span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="px-4 py-2 hover:bg-muted transition-colors"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex gap-4">
                <Button onClick={handleAddToCart} disabled={!product.inStock} className="flex-1" size="lg">
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  Add to Cart - ${(product.price * quantity).toFixed(2)}
                </Button>
                <Button
                  variant={isInWishlist(product.id) ? "default" : "outline"}
                  size="lg"
                  onClick={handleWishlistToggle}
                >
                  <Heart className={`h-5 w-5 ${isInWishlist(product.id) ? "fill-current" : ""}`} />
                </Button>
                <Button
                  variant={isInComparison(product.id) ? "default" : "outline"}
                  size="lg"
                  onClick={handleComparisonToggle}
                >
                  <Scale className="h-5 w-5" />
                </Button>
                <Button variant="outline" size="lg">
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <Truck className="h-6 w-6 text-primary" />
                <div>
                  <div className="font-medium">Free Shipping</div>
                  <div className="text-sm text-muted-foreground">On orders over $50</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <Shield className="h-6 w-6 text-primary" />
                <div>
                  <div className="font-medium">Warranty</div>
                  <div className="text-sm text-muted-foreground">2 year coverage</div>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <RotateCcw className="h-6 w-6 text-primary" />
                <div>
                  <div className="font-medium">Easy Returns</div>
                  <div className="text-sm text-muted-foreground">30 day policy</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-16">
          <Tabs defaultValue="features" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
              <TabsTrigger value="recommendations">Recommended</TabsTrigger>
            </TabsList>

            <TabsContent value="features" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Key Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-primary rounded-full" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Technical Specifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b">
                        <span className="font-medium">{key}</span>
                        <span className="text-muted-foreground">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <ProductReviews productId={product.id} rating={product.rating} reviewCount={product.reviews} />
            </TabsContent>

            <TabsContent value="recommendations" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>You might also like</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {recommendedProducts.map((recommendedProduct) => (
                      <Link key={recommendedProduct.id} href={`/products/${recommendedProduct.id}`}>
                        <div className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <Image
                            src={recommendedProduct.image || "/placeholder.svg"}
                            alt={recommendedProduct.name}
                            width={200}
                            height={200}
                            className="w-full h-32 object-cover rounded-lg mb-2"
                          />
                          <h4 className="font-medium text-sm line-clamp-2 mb-1">{recommendedProduct.name}</h4>
                          <p className="text-lg font-bold">${recommendedProduct.price}</p>
                        </div>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Recently Viewed Products */}
        {recentlyViewed.length > 1 && (
          <div className="mt-16">
            <h3 className="text-2xl font-bold mb-6">Recently Viewed</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {recentlyViewed
                .filter((p) => p.id !== product.id)
                .slice(0, 4)
                .map((recentProduct) => (
                  <Link key={recentProduct.id} href={`/products/${recentProduct.id}`}>
                    <div className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <Image
                        src={recentProduct.image || "/placeholder.svg"}
                        alt={recentProduct.name}
                        width={200}
                        height={200}
                        className="w-full h-32 object-cover rounded-lg mb-2"
                      />
                      <h4 className="font-medium text-sm line-clamp-2 mb-1">{recentProduct.name}</h4>
                      <p className="text-lg font-bold">${recentProduct.price}</p>
                    </div>
                  </Link>
                ))}
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}
