module.exports = {
  ci: {
    collect: {
      // Collect lighthouse data from these URLs
      url: [
        'http://localhost:3000',
        'http://localhost:3000/products',
        'http://localhost:3000/categories',
        'http://localhost:3000/cart',
        'http://localhost:3000/checkout',
      ],
      // Number of times to run lighthouse on each URL
      numberOfRuns: 3,
      // Settings for lighthouse
      settings: {
        // Use desktop preset for consistent results
        preset: 'desktop',
        // Skip certain audits that might not be relevant
        skipAudits: [
          'canonical',
          'uses-http2',
          'redirects-http',
        ],
      },
    },
    assert: {
      // Performance thresholds
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.8 }],
        'categories:best-practices': ['warn', { minScore: 0.8 }],
        'categories:seo': ['warn', { minScore: 0.8 }],
        // Specific metric thresholds
        'first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['warn', { maxNumericValue: 2500 }],
        'cumulative-layout-shift': ['warn', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['warn', { maxNumericValue: 300 }],
      },
    },
    upload: {
      // Configure where to store results (optional)
      target: 'temporary-public-storage',
    },
    // Note: For development testing, make sure your dev server is running on port 3000
    // Run: pnpm dev
    // Then run: pnpm lhci:collect
  },
}
