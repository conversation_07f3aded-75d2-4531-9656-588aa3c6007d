"use client"

import { useState } from "react"
import Link from "next/link"
import { useCart } from "@/contexts/CartContext"
import { useWishlist } from "@/contexts/WishlistContext"
import { useComparison } from "@/contexts/ComparisonContext"
import { useLanguage } from "@/hooks/useLanguage"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { ShoppingCart, Search, Menu, User, Heart, Scale, HelpCircle, Globe } from "lucide-react"
import { motion } from "framer-motion"

export default function Header() {
  const { state: cartState } = useCart()
  const { state: wishlistState } = useWishlist()
  const { state: comparisonState } = useComparison()
  const { language, setLanguage, t } = useLanguage()
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  return (
    <motion.header
      className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/" className="flex items-center space-x-2">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                EliteShop
              </h1>
            </motion.div>
          </Link>

          <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
            <Link href="/products" className="transition-colors hover:text-primary">
              {t("nav.products")}
            </Link>
            <Link href="/categories" className="transition-colors hover:text-primary">
              {t("nav.categories")}
            </Link>
            <Link href="/deals" className="transition-colors hover:text-primary">
              {t("nav.deals")}
            </Link>
            <Link href="/support" className="transition-colors hover:text-primary">
              Support
            </Link>
            <Link href="/admin" className="transition-colors hover:text-primary">
              Admin
            </Link>
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <div className="hidden md:flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("search.placeholder")}
                className="pl-8 w-64"
                onFocus={() => setIsSearchOpen(true)}
                onBlur={() => setIsSearchOpen(false)}
              />
            </div>
          </div>

          <Select value={language} onValueChange={setLanguage}>
            <SelectTrigger className="w-20">
              <Globe className="h-4 w-4" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">EN</SelectItem>
              <SelectItem value="es">ES</SelectItem>
              <SelectItem value="fr">FR</SelectItem>
              <SelectItem value="de">DE</SelectItem>
            </SelectContent>
          </Select>

          <Link href="/support">
            <Button variant="ghost" size="icon">
              <HelpCircle className="h-5 w-5" />
            </Button>
          </Link>

          <Link href="/compare">
            <Button variant="ghost" size="icon" className="relative">
              <Scale className="h-5 w-5" />
              {comparisonState.itemCount > 0 && (
                <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="absolute -top-1 -right-1">
                  <Badge
                    variant="destructive"
                    className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {comparisonState.itemCount}
                  </Badge>
                </motion.div>
              )}
            </Button>
          </Link>

          <Link href="/wishlist">
            <Button variant="ghost" size="icon" className="relative">
              <Heart className="h-5 w-5" />
              {wishlistState.itemCount > 0 && (
                <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="absolute -top-1 -right-1">
                  <Badge
                    variant="destructive"
                    className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {wishlistState.itemCount}
                  </Badge>
                </motion.div>
              )}
            </Button>
          </Link>

          <Button variant="ghost" size="icon" className="relative">
            <User className="h-5 w-5" />
          </Button>

          <Link href="/cart">
            <Button variant="ghost" size="icon" className="relative">
              <ShoppingCart className="h-5 w-5" />
              {cartState.itemCount > 0 && (
                <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="absolute -top-1 -right-1">
                  <Badge
                    variant="destructive"
                    className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {cartState.itemCount}
                  </Badge>
                </motion.div>
              )}
            </Button>
          </Link>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <nav className="flex flex-col space-y-4 mt-8">
                <Link href="/products" className="text-lg font-medium">
                  {t("nav.products")}
                </Link>
                <Link href="/categories" className="text-lg font-medium">
                  {t("nav.categories")}
                </Link>
                <Link href="/deals" className="text-lg font-medium">
                  {t("nav.deals")}
                </Link>
                <Link href="/wishlist" className="text-lg font-medium">
                  {t("nav.wishlist")}
                </Link>
                <Link href="/compare" className="text-lg font-medium">
                  {t("nav.compare")}
                </Link>
                <Link href="/support" className="text-lg font-medium">
                  Support
                </Link>
                <Link href="/admin" className="text-lg font-medium">
                  Admin
                </Link>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </motion.header>
  )
}
