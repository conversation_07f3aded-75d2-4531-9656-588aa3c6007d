[{"name": "minScore", "expected": 0.9, "actual": 0.87, "values": [0.87, 0.87, 0.87], "operator": ">=", "passed": false, "auditProperty": "accessibility", "auditId": "categories", "level": "error", "url": "http://localhost:3000/"}, {"name": "minScore", "expected": 0.9, "actual": 0.87, "values": [0.87, 0.87, 0.87], "operator": ">=", "passed": false, "auditProperty": "accessibility", "auditId": "categories", "level": "error", "url": "http://localhost:3000/products"}, {"name": "minScore", "expected": 0.9, "actual": 0.89, "values": [0.89, 0.89, 0.89], "operator": ">=", "passed": false, "auditProperty": "accessibility", "auditId": "categories", "level": "error", "url": "http://localhost:3000/categories"}]